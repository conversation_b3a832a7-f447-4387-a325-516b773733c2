package mil.afrl.batdok.logging

import android.Manifest
import android.app.Application
import android.content.pm.PackageManager
import android.os.Build
import android.os.Environment
import android.widget.Toast
import batdok.batman.exportlibrary.io.ZipIO
import com.batman.batdok.di.BatdokDispatchers
import gov.afrl.batdok.util.Patterns
import gov.afrl.batdok.util.format
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import mil.af.afrl.batdokdata.models.user.UserDataStore
import java.io.File
import java.time.Instant

/**
 * Use an interface here so we can test it since the implementation uses an application
 */
interface IBatdokAuditor {
    fun log(message: String)
    fun share()
    fun hasExternalWritePermission(): Boolean
    suspend fun canExport(): Boolean
}

class BatdokAuditor(
    val application: Application,
    val appScope: CoroutineScope,
    val batdokDispatchers: BatdokDispatchers,
    val userDatastore: UserDataStore,
    val zipIO: ZipIO,
) : IBatdokAuditor {
    private val logsDir = File(application.filesDir, "logs")

    private suspend fun log(message: String, user: String?) = withContext(batdokDispatchers.io) {
        val logTime = Instant.now()
        val userSuffix = " - $user".takeUnless { user.isNullOrEmpty() } ?: ""
        val timedMessage = "${
            logTime.format(Patterns.ymdhmsS_24_dash_space_colon_dot)
        } - $message$userSuffix"

        File(logsDir, "${logTime.format(Patterns.ymd)}.txt")
            .also {
                if (it.parentFile?.exists() != true) {
                    it.parentFile?.mkdirs()
                }
            }
            .appendText(timedMessage + "\n")
    }

    override fun log(message: String) {
        appScope.launch {
            val username = try {
                userDatastore.getUser().name
            } catch (ex: Exception) {
                //Don't include the username if you can't access the database
                null
            }
            log(message, username)
        }
    }

    override fun share() {
        appScope.launch(batdokDispatchers.io) {
            val zipFile =
                File(Environment.getExternalStorageDirectory(), "BATDOK/BATDOKlogs.zip")
            val logFiles =
                logsDir.listFiles { file -> !file.name.endsWith(".zip") }?.toList() ?: listOf()
            zipIO.zipFilesIntoFile(logFiles, zipFile)
            withContext(batdokDispatchers.main) {
                Toast.makeText(
                    application,
                    "Logs saved to BATDOK/BATDOKlogs.zip",
                    Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    override fun hasExternalWritePermission() = when {
        Build.VERSION.SDK_INT < Build.VERSION_CODES.R ->
            application.checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED

        else -> Environment.isExternalStorageManager()
    }

    override suspend fun canExport() = withContext(batdokDispatchers.io) {
        logsDir.exists() && !logsDir.list().isNullOrEmpty() && hasExternalWritePermission()
    }
}