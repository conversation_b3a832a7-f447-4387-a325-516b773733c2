package mil.afrl.batdok.preferences.display

import android.content.SharedPreferences
import mil.afrl.batdok.preferences.BatdokPreference

enum class QrDocType(val displayString: String) {
    DOCUMENTATION("BATDOK-J"),
    HL7_GT("Genesis Theater (via Link)"),
    HL7_OMDS("OMDS (via Link)"),
    HL7_CDP("OpMed CDP"),
    HL7_HALO("HALO"),
    ATMIST("ATMIST"),
    MACE("BATDOK-J (MACE)");

    companion object {
        fun fromDisplayString(string: String?) =
            entries.firstOrNull { it.displayString == string } ?: DOCUMENTATION
    }
}

data object HandoffTargetAppPreference : BatdokPreference<QrDocType> {
    private const val HandoffTargetAppPreferenceName = "handoff_target_app"
    override fun SharedPreferences.get(): QrDocType {
        return QrDocType.entries[getInt(HandoffTargetAppPreferenceName, 0)]
    }

    override fun SharedPreferences.Editor.set(data: QrDocType) {
        putInt(HandoffTargetAppPreferenceName, data.ordinal)
    }

}