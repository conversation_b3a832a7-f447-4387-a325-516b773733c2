package gov.afrl.batdok.documentation.ui.handoff


import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_EXPORTED
import android.content.ContextWrapper
import android.content.IntentFilter
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.outlined.OpenInNew
import androidx.compose.material.icons.automirrored.outlined.StickyNote2
import androidx.compose.material.icons.outlined.Nfc
import androidx.compose.material.icons.outlined.PictureAsPdf
import androidx.compose.material.icons.outlined.Print
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands
import gov.afrl.batdok.documentation.ui.IUnifiedDocumentViewModel
import gov.afrl.batdok.documentation.ui.TestUnifiedDocumentViewModel
import gov.afrl.batman.batdok.dialogs.*
import gov.afrl.batman.batdok.dialogs.hl7.CertificatesNeededDialog
import gov.afrl.batman.batdok.dialogs.hl7.ExchangeCompleteDialog
import gov.afrl.batman.batdok.dialogs.hl7.ExchangeDialogState
import gov.afrl.batman.batdok.dialogs.hl7.NetworkEndpointChooserDialog
import gov.afrl.batman.batdok.dialogs.nfc.IWriteNfcDialogViewModel
import gov.afrl.batman.batdok.dialogs.nfc.TestWriteNfcDialogViewModel
import gov.afrl.batman.batdok.dialogs.nfc.rememberNfcWriteState
import gov.afrl.batman.batdok.views.QrCodeViewer
import gov.batman.batdok.theme.SDCTheme
import gov.batman.batdok.theme.components.buttons.SDCTextButton
import gov.batman.batdok.theme.components.input.SDCDropdown
import gov.batman.batdok.theme.components.selection.SDCSelectionGrid
import gov.batman.batdok.theme.components.selection.rememberSelectionState
import gov.batman.batdok.theme.components.structure.SDCElevatedCard
import gov.batman.batdok.theme.components.structure.SDCScaffold
import gov.batman.batdok.theme.components.structure.SDCSurface
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.MhsgtNetworkEndpoint
import mil.af.afrl.batman.hl7lib.dataexchanger.MhsgtDataExchanger
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.hasCertException
import mil.af.afrl.batman.hl7lib.util.hasCertException
import mil.afrl.batdok.preferences.display.QrDocType

import javax.crypto.SecretKey

@Composable
fun HandoffView(
    encounterId: EncounterId,
    handoffViewModel: IHandoffViewModel,
    shareDocumentViewModel: IShareDocumentViewModel,
    writeNfcDialogViewModel: IWriteNfcDialogViewModel,
    documentViewModel: IUnifiedDocumentViewModel,
    showPCS: Boolean,
    patientCareSummary: @Composable () -> Unit
) {
    val message by remember { handoffViewModel.getPatientNfcMessage() }.collectAsState(initial = null)

    DisposableEffect(key1 = encounterId) {
        handoffViewModel.setEncounterId(encounterId)
        documentViewModel.setEncounterId(encounterId)
        onDispose {
            handoffViewModel.setEncounterId(null)
            handoffViewModel.clearQRCode()
        }
    }

    LaunchedEffect(key1 = message, key2 = encounterId, block = {
        handoffViewModel.getAvailableQrCodes()
    })

    Row(
        horizontalArrangement = Arrangement.spacedBy(5.dp)
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxSize()
        ) {
            if (showPCS) {
                patientCareSummary()
            } else {
                TransferDataContents(
                    encounterId = encounterId,
                    handoffViewModel = handoffViewModel,
                    shareDocumentViewModel = shareDocumentViewModel,
                    documentViewModel = documentViewModel,
                    writeNfcDialogViewModel = writeNfcDialogViewModel,
                    encryptionKey = handoffViewModel.getEncryptionKey(),
                    broadcastReceiver = handoffViewModel.getBroadcastReceiver(),
                    message = message
                )
            }
        }
    }

}

@Composable
private fun IconWithTextButton(
    text: String,
    icon: ImageVector,
    testTag: String = text,
    onClick: () -> Unit,
) {
    SDCTextButton(
        style = SDCTheme.colorScheme.button.tertiaryNav,
        onClick = onClick,
        modifier = Modifier.testTag(testTag)
    ) {
        Icon(icon, contentDescription = null, modifier = Modifier.size(18.dp))
        Spacer(Modifier.width(8.dp))
        Text(text)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TransferDataContents(
    encounterId: EncounterId,
    handoffViewModel: IHandoffViewModel,
    shareDocumentViewModel: IShareDocumentViewModel,
    documentViewModel: IUnifiedDocumentViewModel,
    writeNfcDialogViewModel: IWriteNfcDialogViewModel,
    encryptionKey: SecretKey,
    broadcastReceiver: BroadcastReceiver,
    message: ContactlessTransferCommands.ContactlessTransferMessage?,
) {

    fun Context.findActivity(): Activity? = when (this) {
        is Activity -> this
        is ContextWrapper -> baseContext.findActivity()
        else -> null
    }

    val context = LocalContext.current
    val shareDialogState = rememberAlertDialogState()
    val extraQrDataSelectionState = rememberSelectionState<ExtraQrData>()

    val animatedQRDialogState = rememberAlertDialogState()
    val qrCodeData = handoffViewModel.qrCodeData

    //region HL7 handling stuff
    var gtEndpoint by remember { mutableStateOf(handoffViewModel.getGtEndpoint()) }

    var hl7Data by remember { mutableStateOf(OutboundHL7Data()) }
    LaunchedEffect(true) {
        // Never include historical HL7 here since it only goes to MHSG-T
        hl7Data = handoffViewModel.buildBaseOutboundHL7Data(false)
    }

    val exportCompletedState = remember { ExchangeDialogState(false) }
    ExchangeCompleteDialog(exportCompletedState)

    val certMissingState = remember { AlertDialogState() }
    CertificatesNeededDialog(certMissingState)

    // Shown only to allow the user to set an outbound endpoint
    val endpointChooserState = rememberAlertDialogState()
    NetworkEndpointChooserDialog(
        endpointChooserState,
        exporting = hl7Data.exporting,
        endpointOptions = listOf(gtEndpoint),
    ) {
        handoffViewModel.setLastEndpointType(it.format)
        // Only ever GT
        gtEndpoint = it as MhsgtNetworkEndpoint
        handoffViewModel.setGtEndpoint(gtEndpoint)
        hl7Data.endpoint = it
        MhsgtDataExchanger.exchangeData(
            context, hl7Data,
            onStatusUpdate = {
                handoffViewModel.setPatientExportStatus(encounterId, "handoff", it.code ?: -1)
                documentViewModel.logAuditAction(
                    "HL7 Export -- MHSG-T responded with status ${it.code} for patient ${
                        shareDocumentViewModel.getPatientIdentifier(
                            encounterId
                        )
                    }"
                )
                if (hasCertException(it.error)) {
                    certMissingState.show()
                } else {
                    exportCompletedState.statusCode = it.code ?: -1
                    exportCompletedState.show()
                }
            }
        )
    }
    //endregion

    ShareDocumentDialog(
        alertDialogState = shareDialogState,
        encounterId = encounterId,
        shareDocumentViewModel = shareDocumentViewModel
    )



    AnimatedQRCodeDialog(alertDialogState = animatedQRDialogState, qrCodes = qrCodeData.multiQrCode)

    val nfcWriteState = rememberNfcWriteState(message?.toByteArray())
    NFCDialog(
        message,
        writeNfcDialogViewModel,
        nfcWriteState
    )

    Row {
        QrCodeViewer(
            modifier = Modifier.weight(.3f),
            qrCodeName = qrCodeData.qrCodeName,
            qrCodes = if (qrCodeData.splitQrCodeSuccess) {
                qrCodeData.multiQrCode
            } else if (qrCodeData.qrCodeSuccess && qrCodeData.qrCode != null) {
                listOf(qrCodeData.qrCode!!)
            } else {
                null
            },
            errorMessage = if (qrCodeData.splitQrCodeSuccess || qrCodeData.qrCodeSuccess) null else "Creating QR Codes",
            showHeader = false,
            qrCodeSuccess = (qrCodeData.splitQrCodeSuccess || qrCodeData.qrCodeSuccess)
        ) {
            animatedQRDialogState.show()
        }
        Column(
            modifier = Modifier
                .weight(.4725f)
                .fillMaxHeight()
                .padding(horizontal = 20.dp),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            LaunchedEffect(Unit) {
                handoffViewModel.getAvailableQrCodes()
            }
            var isExpanded by remember { mutableStateOf(false) }
            val receivingApplicationsList by handoffViewModel.qrCodeState().collectAsState(mapOf())
            val mostRecentQrCode =
                receivingApplicationsList[handoffViewModel.docType]?.maxByOrNull { it.createTime }
            LaunchedEffect(mostRecentQrCode, extraQrDataSelectionState.selectedItems) {
                mostRecentQrCode?.let {
                    handoffViewModel.generateQrCode(
                        context,
                        it.qrCodeData,
                        extraQrDataSelectionState.selectedItems.toSet()
                    )
                        if (it.docType == QrDocType.HL7_HALO.displayString || it.docType == QrDocType.HL7_CDP.displayString || it.docType == QrDocType.HL7_OMDS.displayString || it.docType == QrDocType.HL7_GT.displayString) {
                            documentViewModel.logAuditAction("HL7 Export -- QR Code type ${it.docType} selected for handoff")
                        }
                    }
            }
            val labelStyle = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                lineHeight = 16.sp,
                letterSpacing = 0.25.sp,
                color = SDCTheme.colorScheme.defaults.content.tertiary
            )

            Spacer(Modifier.height(20.dp))

            Text("Select Receiving Application", style = labelStyle)
            SDCDropdown(
                handoffViewModel.docType.displayString,
                isExpanded,
                { isExpanded = !isExpanded },
                receivingApplicationsList.keys.map { it.displayString },
                onFinalValueChange = { value ->
                    handoffViewModel.docType = QrDocType.fromDisplayString(value)
                    isExpanded = false
                },
                innerTextFieldModifier = Modifier.fillMaxWidth(),
            )

            HorizontalDivider(Modifier.padding(vertical = 12.dp))
            Text("Data to include (Current and Past Encounters):", style = labelStyle)

            val isDocumentation = handoffViewModel.docType == QrDocType.DOCUMENTATION
            SDCSelectionGrid(
                ExtraQrData.entries.map { it to it.text },
                if (isDocumentation) extraQrDataSelectionState else rememberSelectionState(),
                modifier = Modifier.padding(vertical = 12.dp),
                maximumSelectableItems = if (isDocumentation) Int.MAX_VALUE else 0,
                isSingleSelect = false,
                itemsPerRow = 2,
            )
        }
        SDCElevatedCard(
            Modifier
                .weight(.2275f)
                .fillMaxHeight()
                .verticalScroll(rememberScrollState())
        ) {
            val labelStyle = TextStyle(
                color = SDCTheme.colorScheme.defaults.content.quaternary,
                fontWeight = FontWeight.SemiBold,
                fontSize = 12.sp,
                letterSpacing = 1.sp,
                lineHeight = 16.sp,
            )
            // Because buttons have their own padding, we have to manually overlap the buttons in this column so they can fit
            val negativeVerticalPadding = (-6).dp
            Column(
                Modifier.align(Alignment.CenterHorizontally),
                verticalArrangement = Arrangement.spacedBy(negativeVerticalPadding)
            ) {
                val lifecycleOwner = LocalLifecycleOwner.current
                var nfcButtonText by remember { mutableStateOf<String?>(null) }
                LaunchedEffect(Unit) {
                    lifecycleOwner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                        nfcButtonText = when (handoffViewModel.getNfcAdapterState(context)) {
                            IHandoffViewModel.NfcAdapterState.UNAVAILABLE -> null
                            IHandoffViewModel.NfcAdapterState.DISABLED -> "NFC Disabled"
                            else -> "NFC Ready"
                        }
                    }
                }

                Text(
                    "ALTERNATE HANDOFF",
                    style = labelStyle,
                    modifier = Modifier.padding(top = 20.dp, bottom = -negativeVerticalPadding)
                )
                nfcButtonText?.let {
                    IconWithTextButton(it, Icons.Outlined.Nfc, "NFC Button") {
                        nfcWriteState.promptWrite(
                            context
                        )
                    }
                }
                IconWithTextButton(
                    "HL7 Direct",
                    Icons.AutoMirrored.Outlined.StickyNote2
                ) { endpointChooserState.show() }
                IconWithTextButton("Open in App", Icons.AutoMirrored.Outlined.OpenInNew) {
                    context.registerReceiver(
                        broadcastReceiver,
                        IntentFilter(INTERNAL_CHOOSER_ACTION),
                        RECEIVER_EXPORTED
                    )
                    val chooser = createApp2AppChooser(context, encounterId, encryptionKey)
                    context.startActivity(chooser)
                }
                Text(
                    "GENERATE PDF FORMS",
                    style = labelStyle,
                    modifier = Modifier.padding(top = 16.dp, bottom = -negativeVerticalPadding)
                )
                IconWithTextButton(
                    "Share Forms",
                    Icons.Outlined.PictureAsPdf
                ) { shareDialogState.show() }
                IconWithTextButton("Print Forms", Icons.Outlined.Print) {
                    handoffViewModel.onPrint(
                        context.findActivity()!!,
                        encounterId
                    )
                }
            }
        }
    }
}

@Preview(widthDp = 800, heightDp = 360, showBackground = true)
@Composable
fun PreviewHandoffTabsLight() {
    SDCTheme(
        darkTheme = false
    ) {
        SDCScaffold {
            HandoffView(
                DomainId.create(),
                TestHandoffViewModel(),
                TestShareDocumentViewModel(),
                TestWriteNfcDialogViewModel(),
                TestUnifiedDocumentViewModel(),
                showPCS = false,
                { }
            )
        }
    }
}

@Preview(widthDp = 800, heightDp = 360, showBackground = true)
@Composable
fun PreviewHandoffTabsDark() {
    SDCTheme(
        darkTheme = true
    ) {
        SDCSurface(depth = 1.dp) {
            HandoffView(
                DomainId.create(),
                TestHandoffViewModel(),
                TestShareDocumentViewModel(),
                TestWriteNfcDialogViewModel(),
                TestUnifiedDocumentViewModel(),
                showPCS = false,
                { }
            )
        }
    }
}

@Preview(widthDp = 800, heightDp = 360, showBackground = true)
@Composable
fun PreviewTransferDataContents() {
    SDCTheme {
        SDCScaffold {
            val vm = TestHandoffViewModel()
            val vm3 = TestHandoffViewModel()

            TransferDataContents(
                encounterId = DomainId.create(),
                handoffViewModel = vm3,
                shareDocumentViewModel = remember {
                    TestShareDocumentViewModel(
                        pdfs = listOf("PDF 1", "PDF 2", "PDF 3"),
                        textFiles = listOf("File 1", "File 2", "File 3"),
                        mediaFiles = listOf("Media 1", "Media 2", "Media 3"),
                        vitals = listOf("Vital 1", "Vital 2", "Vital 3")
                    )
                },
                documentViewModel = remember {
                    TestUnifiedDocumentViewModel()
                },
                writeNfcDialogViewModel = TestWriteNfcDialogViewModel(),
                encryptionKey = vm.getEncryptionKey(),
                broadcastReceiver = vm.getBroadcastReceiver(),
                message = vm.getPatientNfcMessage().collectAsState(null).value,

                )
        }

    }
}