[versions]
agp = "8.7.3"
androidx-junit = "1.2.1"
barcodeScanning = "17.3.0"
batdokSensorPlugins = "5.0.3"
camerax = "1.4.1"
datastore = "1.1.3"
#Had to downgrade espresso due to another lib having it strictly set to 3.5.0
espresso-core = "3.5.0"
espressoIntents = "3.5.0"
junit = "1.1.3"
junit4compose = "1.7.8"
kotlin = "2.1.10"
kotlinx = '1.8.1'
kotlinxSerializationJson = "1.8.0"
ksp = "0.7.0"
lifecycleExtensions = "2.2.0"
lifecycleExtensions-ktx = "2.8.7"
material = "1.12.0"
media3Exoplayer = "1.5.1"
mockitoInline = "3.12.4"
mockitoKotlin = "5.4.0"
mockk = "1.13.12"
orchestrator = "1.5.1"
org-jetbrains-kotlin-android = "2.1.20"
com-google-devtools-ksp = "2.1.20-1.0.32"
robolectric = "4.14.1"
room = "2.6.1"
symbolProcessingApiVersion = "2.1.10-1.0.31"
threetenbp = "1.6.2"
uiautomator = "2.3.0"
koin = "4.0.0"
theming = "4.0.0"
jetbrainsKotlinJvm = "2.1.10"
runner = "1.6.2"
coreKtx = "1.16.0"
lifecycleViewmodelKtx = "2.2.0"

[libraries]

# ### androidx.activity ###
barcode-scanning = { module = "com.google.mlkit:barcode-scanning", version.ref = "barcodeScanning" }
c2data = { module = "mil.af.afrl:c2data", version = "1.1.1" }
compose_activity = { module = "androidx.activity:activity-compose", version = "1.10.1" }

# ### androidx.appcompat ###
appcompat = { group = "androidx.appcompat", name = "appcompat", version = "1.7.0" }

# ### androidx.arch ###
androidx-core-testing = { module = "androidx.arch.core:core-testing", version = "2.2.0" }

# ### androidx.camera ###
camera = { module = "androidx.camera:camera-camera2", version.ref = "camerax" }
camera_lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "camerax" }
camera_view = { module = "androidx.camera:camera-view", version.ref = "camerax" }

# ### androidx.cardview ###
androidx-cardview = { module = "androidx.cardview:cardview", version = "1.0.0" }

# ### androidx.compose ###
compose_bom = { module = "androidx.compose:compose-bom", version = "2025.02.00" }
compose_material3 = { module = "androidx.compose.material3:material3", version = "1.3.1" }
compose_foundation = { module = "androidx.compose.foundation:foundation", version = "1.7.8" } #https://developer.android.com/reference/kotlin/androidx/compose/foundation/layout/package-summary#FlowRow(androidx.compose.ui.Modifier,androidx.compose.foundation.layout.Arrangement.Horizontal,androidx.compose.foundation.layout.Arrangement.Vertical,kotlin.Int,kotlin.Int,androidx.compose.foundation.layout.FlowRowOverflow,kotlin.Function1)
compose_ui = { module = "androidx.compose.ui:ui" }
compose_ui_tooling_preview = { module = "androidx.compose.ui:ui-tooling-preview" }
compose_ui_viewbinding = { module = "androidx.compose.ui:ui-viewbinding" }
compose_livedata = { module = "androidx.compose.runtime:runtime-livedata" }
compose_rxjava2 = { module = "androidx.compose.runtime:runtime-rxjava2" }
compose_ui_tooling = { module = 'androidx.compose.ui:ui-tooling' }
compose_ui_test_junit4 = { module = "androidx.compose.ui:ui-test-junit4", version.ref = "junit4compose" }
compose_ui_test_manifest = { module = "androidx.compose.ui:ui-test-manifest", version.ref = "junit4compose" }

# ### androidx.constraintlayout ###
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version = "2.2.1" }

# ### androidx.core ###
core-ktx = { group = "androidx.core", name = "core-ktx", version = "1.15.0" }

# ### androidx.datastore ###

prefs = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastore" }
datastore = { group = "androidx.datastore", name = "datastore", version.ref = "datastore" }

# ### androidx.exifinterface ###
androidx-exifinterface = { module = "androidx.exifinterface:exifinterface", version = "1.4.0" }

# ### androidx.fragment ###
androidx-fragment-ktx = { module = "androidx.fragment:fragment-ktx", version = "1.8.6" }

# ### androidx.legacy ###
androidx-legacy-support-core-utils = { module = "androidx.legacy:legacy-support-core-utils", version = "1.0.0" }

# ### androidx.lifecycle ###
androidx-lifecycle-extensions = { module = "androidx.lifecycle:lifecycle-extensions", version.ref = "lifecycleExtensions" }
androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycleExtensions-ktx" }
lifecycle-reactivestreams-ktx = { module = "androidx.lifecycle:lifecycle-reactivestreams-ktx", version.ref = "lifecycleExtensions-ktx" }
compose_lifecycle_runtime = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleExtensions-ktx" }
compose_viewmodel = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleExtensions-ktx" }

# ### androidx.media3 ###
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3Exoplayer" }
androidx-media3-exoplayer-dash = { module = "androidx.media3:media3-exoplayer-dash", version.ref = "media3Exoplayer" }
androidx-media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3Exoplayer" }

# ### androidx.multidex ###
androidx-multidex = { module = "androidx.multidex:multidex", version = "2.0.1" }

# ### androidx.preference ###
androidx-preference-ktx = { module = "androidx.preference:preference-ktx", version = "1.2.1" }

# ### androidx.recyclerview ###
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version = "1.4.0" }

# ### androidx.room ###
room_runtime = { module = "androidx.room:room-runtime", version.ref = "room" }
room_ktx = { module = "androidx.room:room-ktx", version.ref = "room" }
room_compiler = { module = "androidx.room:room-compiler", version.ref = "room" }
room_rxjava2 = { module = "androidx.room:room-rxjava2", version.ref = "room" }
room_testing = { module = "androidx.room:room-testing", version.ref = "room" }

# ### androidx.savestate ###
androidx-savedstate = { module = 'androidx.savedstate:savedstate-ktx', version = '1.2.1' }

# ### androidx.sqlite ###
sqlite = { module = "androidx.sqlite:sqlite-ktx", version = "2.4.0" }

# ### androidx.test ###
androidx-core = { module = "androidx.test:core", version = "1.6.1" }
androidx-espresso-contrib = { module = "androidx.test.espresso:espresso-contrib", version.ref = "espressoIntents" }
androidx-espresso-intents = { module = "androidx.test.espresso:espresso-intents", version.ref = "espressoIntents" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espresso-core" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "androidx-junit" }
androidx-junit-ktx = { module = "androidx.test.ext:junit-ktx", version.ref = "androidx-junit" }
androidx-orchestrator = { module = "androidx.test:orchestrator", version.ref = "orchestrator" }
androidx-rules = { module = "androidx.test:rules", version = "1.5.0" }
androidx-runner = { module = "androidx.test:runner", version = "1.5.0" }
androidx-uiautomator = { module = "androidx.test.uiautomator:uiautomator", version.ref = "uiautomator" }

# ### androidx.viewpager2 ###
androidx-viewpager2 = { module = "androidx.viewpager2:viewpager2", version = "1.1.0" }

# ### app.cash ###
threetenbp = { module = "org.threeten:threetenbp", version.ref = "threetenbp" }
turbine = { module = "app.cash.turbine:turbine", version = "1.1.0" }

# ### com.android ###
gradle = { module = "com.android.tools.build:gradle", version = "8.8.2" }
volley = { module = "com.android.volley:volley", version = "1.2.1" }

# ### com.caverock ###
androidsvg-aar = { module = "com.caverock:androidsvg-aar", version = "1.4" }

# ### com.getkeepsafe ###
taptargetview = { module = "com.getkeepsafe.taptargetview:taptargetview", version = "1.13.3" }

# ### com.github ###
mpandroidchart = { module = "com.github.PhilJay:MPAndroidChart", version = "v3.0.2" }
signature-pad = { module = "com.github.gcacace:signature-pad", version = "1.3.1" }
toasthandler = { module = "com.github.niharika2810:ToastHandler", version = "1.2.0" }
touchimageview = { module = "com.github.MikeOrtiz:TouchImageView", version = "7edf46d" } # Author removed 3.0.3 release from github. Using short commit from 3.0.3 tag

# ### com.google.accomponist ###
accomponist = { module = "com.google.accompanist:accompanist-permissions", version = "0.32.0" }

# ### com.google.android ###
flexbox = { module = "com.google.android.flexbox:flexbox", version = "3.0.0" }
material = { group = "com.google.android.material", name = "material", version = "1.12.0" }
play-services-location = { module = "com.google.android.gms:play-services-location", version = "21.3.0" }

# ### com.google.code ###
gson = { module = "com.google.code.gson:gson", version = "2.10.1" }

# ### com.google.gms ###
google-services = { module = "com.google.gms:google-services", version = "4.4.2" }

# ### com.google.guava ###
guava = { module = "com.google.guava:guava", version = "33.3.1-android" }

# ### com.google.protobuf ###
protobuf-gradle-plugin = { module = "com.google.protobuf:protobuf-gradle-plugin", version = "0.9.4" }
protobuf_kotlin = { module = "com.google.protobuf:protobuf-kotlin", version = "3.21.12" }

# ### com.jakewharton ###
rxbinding = { module = "com.jakewharton.rxbinding2:rxbinding", version = "2.2.0" }
rxbinding_support_v4 = { module = "com.jakewharton.rxbinding2:rxbinding-support-v4", version = "2.2.0" }
threetenabp = { module = "com.jakewharton.threetenabp:threetenabp", version = "1.4.1" }

# ### com.journeyapps ###
zxing-android-embedded = { module = "com.journeyapps:zxing-android-embedded", version = "4.3.0" }

# ### com.neenbedankt ###
android-apt = { module = "com.neenbedankt.gradle.plugins:android-apt", version = "1.4" }

# ### com.squareup ###
rx2-idler = { module = "com.squareup.rx.idler:rx2-idler", version = "0.11.0" }

# ### com.starmicronics ###
stario = { module = "com.starmicronics:stario", version = "2.10.1" }
starioextension = { module = "com.starmicronics:starioextension", version = "1.15.1" }

# ### com.tom-roush ###
pdfbox-android = { module = "com.tom-roush:pdfbox-android", version = "2.0.0.0" }

# ### com.vdurmont ###
semver4j = { module = "com.vdurmont:semver4j", version = "3.1.0" }

# ### io.insert-koin ###

koin-bom = { module = "io.insert-koin:koin-bom", version.ref = "koin" }
koin-core = { module = "io.insert-koin:koin-core" }
koin-android = { module = "io.insert-koin:koin-android" }
koin-android-compat = { module = "io.insert-koin:koin-android-compat" }
koin-compose = { module = "io.insert-koin:koin-androidx-compose" }
koin-test = { module = "io.insert-koin:koin-test" }
koin-junit = { module = "io.insert-koin:koin-test-junit4" }

# ### io.mockk ###
mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
mockk-android = { module = "io.mockk:mockk-android", version.ref = "mockk" }

# ### io.reactivex ###
rxandroid = { module = "io.reactivex.rxjava2:rxandroid", version = "2.1.1" }
rxjava = { module = "io.reactivex.rxjava2:rxjava", version = "2.2.21" }
rxkotlin = { module = "io.reactivex.rxjava2:rxkotlin", version = "2.4.0" }

# ### javax.annotation ###
jsr250-api = { module = "javax.annotation:jsr250-api", version = "1.0" }

# ### junit ###
junit = { group = "junit", name = "junit", version = "4.13.2" }

# ### me.tatarka ###
gradle-retrolambda = { module = "me.tatarka:gradle-retrolambda", version = "3.6.0" }

# ### mil.af ###
data-bom = { module = "mil.health.batdok:data-bom", version = "2025.06.24"}
batdokexport = { module = "mil.af.afrl:batdokexport", version = "5.1.0-alpha6" }
pdf-robots = { module = "mil.af.afrl:pdf-robots", version = "5.1.0-alpha6"  }
batdokdata = { module = "mil.af.afrl:batdokdata", version = "9.2.0-alpha4"}
batdokstorage-room = { module = "mil.af.afrl:batdokstorage-room", version = "11.2.0-alpha2" }
batdokstorage-test = { module = "mil.af.afrl:batdokstorage-test", version = "11.2.0-alpha2"}
batdok-protobuf = { module = "mil.af.afrl:batdokprotobuf", version = "7.3.0-alpha2"}
unified-document = { module = "mil.af.afrl:batdok-document", version = "7.1.0-rd-3" }
batdokhl7library = { module = "mil.af.afrl:hl7library", version = "7.2.0-rd-22-SNAPSHOT" }


medicationsdata = { module = "mil.af.afrl:medicationsdata", version = "1.0.1" }
batdokdialog = { module = "mil.af.afrl:batdokdialog", version = "2.0.4" }
batdoka2alibrary = { module = "mil.af.afrl:batdoka2alibrary", version = "4.0.1" }
batdokstorage-prefs = { module = "mil.af.afrl:batdokstorage-prefs", version = "0.1.1" }
medications = { module = "mil.af.afrl:medications", version = "1.3.4-alpha2" }
sensor-base = { module = "mil.af.afrl:sensor-base", version = "1.0.0" }
batdokId = { module = "mil.af.afrl:batdokId", version = "3.0.3" }
encryption = { module = "mil.af.afrl:encryption", version = "2.1.1" }
intentifier = { module = "mil.af.afrl:intentifier", version = "3.0.4" }
theming = { module = "mil.af.afrl:theming", version.ref = "theming" }
theming-robots = { module = "mil.af.afrl:theming-robots", version.ref = "theming" }

fhir = {module = "mil.af.afrl:fhirstarter", version = "0.3.0"}

# ### mil.nga ###
mgrs = { module = "mil.nga:mgrs", version = "2.1.2" }

# ### net.zetetic ###
android-database-sqlcipher = { module = "net.zetetic:android-database-sqlcipher", version = "4.5.4" }

# ### org.jetbrains ###
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version = "2.1.21" }
kotlin_stdlib_jdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8", version.ref = "kotlin" }
kotlin_reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }
kotlinx_coroutines_core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinx" }
kotlinx_coroutines_test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinx" }
kotlinx_coroutines_android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinx" }
kotlinx_coroutines_rx2 = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-rx2", version.ref = "kotlinx" }
kotlinx_serialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }

# ### org.jgroups ###
jgroups = { module = "org.jgroups:jgroups", version = "5.4.1.Final" }

# ### org.json ###
json = { module = "org.json:json", version = "20220924" }

# ### org.mockito ###
mockito-inline = { module = "org.mockito:mockito-inline", version.ref = "mockitoInline" }
mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version.ref = "mockitoKotlin" }

# ### org.robolectric ###
robolectric = { module = "org.robolectric:robolectric", version.ref = "robolectric" }
shadows-multidex = { module = "org.robolectric:shadows-multidex", version.ref = "robolectric" }

# ### org.scijava ###
native-lib-loader = { module = "org.scijava:native-lib-loader", version = "2.4.0" }

# ### xyz.danoz ###
recyclerviewfastscroller = { module = "xyz.danoz:recyclerviewfastscroller", version = "0.1.3" }

# ### squareup.okhttp ###
okhttp = { module = "com.squareup.okhttp3:okhttp", version = "4.12.0" }

# ### vosk ###
voskAndroid = { module = "com.alphacephei:vosk-android", version = "0.3.47" }
voskModelEnglish = { module = "com.alphacephei:vosk-model-en", version = "0.3.45" }
runner = { group = "androidx.test", name = "runner", version.ref = "runner" }

# ### ksp ###
ksp = { module = "dev.zacsweers.kctfork:ksp", version.ref = "ksp" }
ksp-symbol-processing-api = { module = "com.google.devtools.ksp:symbol-processing-api", version.ref = "symbolProcessingApiVersion" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycleViewmodelKtx" }


[bundles]

compose = [
    "compose_material3",
    "compose_lifecycle_runtime",
    "compose_foundation",
    "compose_ui",
    "compose_ui_viewbinding",
    "compose_ui_tooling_preview",
    "compose_activity",
    "compose_viewmodel",
    "compose_livedata",
    "compose_rxjava2",
]
rxjava = ["rxandroid", "rxjava", "rxkotlin"]
room = ["sqlite", "room_runtime", "room_ktx", "room_rxjava2"]
camera = ["camera", "camera_lifecycle", "camera_view"]
koin = [
    "koin-core",
    "koin-android",
    "koin-compose",
    "koin-android-compat"
]
koin-test = [
    "koin-test",
    "koin-junit"
]
[plugins]
com-android-library = { id = "com.android.library", version.ref = "agp" }
org-jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version = "2.1.21" }
com-google-devtools-ksp = { id = "com.google.devtools.ksp", version.ref = "com-google-devtools-ksp" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
protobuf = { id = "com.google.protobuf", version = "0.9.4" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
jetbrains-kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "jetbrainsKotlinJvm" }
