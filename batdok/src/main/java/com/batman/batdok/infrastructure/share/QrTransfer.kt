package com.batman.batdok.infrastructure.share

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import batdok.batman.encryptionlibrary.EncryptionTool
import batdok.batman.encryptionlibrary.QRMapperHelper
import com.batman.batdok.domain.export.IBatdokHeaderVersion
import com.batman.batdok.domain.export.QrBatdokHeader
import com.batman.batdok.domain.export.QrHeaderVersion
import com.batman.batdok.domain.notification.MakeToastNotification
import com.batman.batdok.domain.notification.NfcPatientScannedNotification
import com.batman.batdok.domain.notification.publishNotification
import com.batman.batdok.domain.usecase.SaveMaceUseCase
import com.batman.batdok.domain.usecase.encounter.WaitForEncounterUseCase
import com.batman.batdok.infrastructure.contactlesstransfer.proto.ContactlessTransferCommands
import com.batman.batdok.infrastructure.contactlesstransfer.proto.maceMessageOrNull
import com.batman.batdokdialoglibrary.INotify
import com.batman.batdokdialoglibrary.SafeNotify
import gov.afrl.batdok.encounter.ids.toDomainId
import gov.afrl.batman.batdok.components.BatdokQrData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import mil.af.afrl.batdokdata.id.PatientId
import mil.af.afrl.batman.batdokid.DomainId
import mil.af.afrl.batman.hl7lib.data.models.v231.receiver.message.MessageReceiver
import mil.af.afrl.batman.hl7lib.proto.BeamOuterClass
import mil.af.afrl.batman.hl7lib.proto.Gt
import mil.af.afrl.batman.hl7lib.util.EncryptionUtil
import mil.af.afrl.batman.hl7lib.util.QrCodeGenerator
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream
import kotlin.math.ceil

fun Long.negativeOrGreaterThan(long: Long) = this < 0 || this > long

fun Long.calculateVLongSize(): Int {
    var size = 1
    if (this < 0) size++
    if (this.negativeOrGreaterThan(0xFFFFFFFFFFFFFFL)) size++
    if (this.negativeOrGreaterThan(0x1FFFFFFFFFFFFL)) size++
    if (this.negativeOrGreaterThan(0x3FFFFFFFFFFL)) size++
    if (this.negativeOrGreaterThan(0x7FFFFFFFFL)) size++
    if (this.negativeOrGreaterThan(0xFFFFFFFL)) size++
    if (this.negativeOrGreaterThan(0x1FFFFFL)) size++
    if (this.negativeOrGreaterThan(0x3FFFL)) size++
    if (this.negativeOrGreaterThan(0x7FL)) size++
    return size
}

fun ByteBuffer.putVLong(value: Long) {
    if (value < 0) this.put(0x81.toByte())
    if (value.negativeOrGreaterThan(0xFFFFFFFFFFFFFFL)) this.put((0x80 or (value ushr 56 and 0x7FL).toInt()).toByte())
    if (value.negativeOrGreaterThan(0x1FFFFFFFFFFFFL)) this.put((0x80 or (value ushr 49 and 0x7FL).toInt()).toByte())
    if (value.negativeOrGreaterThan(0x3FFFFFFFFFFL)) this.put((0x80 or (value ushr 42 and 0x7FL).toInt()).toByte())
    if (value.negativeOrGreaterThan(0x7FFFFFFFFL)) this.put((0x80 or (value ushr 35 and 0x7FL).toInt()).toByte())
    if (value.negativeOrGreaterThan(0xFFFFFFFL)) this.put((0x80 or (value ushr 28 and 0x7FL).toInt()).toByte())
    if (value.negativeOrGreaterThan(0x1FFFFFL)) this.put((0x80 or (value ushr 21 and 0x7FL).toInt()).toByte())
    if (value.negativeOrGreaterThan(0x3FFFL)) this.put((0x80 or (value ushr 14 and 0x7FL).toInt()).toByte())
    if (value.negativeOrGreaterThan(0x7FL)) this.put((0x80 or (value ushr 7 and 0x7FL).toInt()).toByte())
    this.put((value and 0x7FL).toByte())
}

fun ByteBuffer.getVLong(): Long {
    var b: Int = this.get().toInt()
    if (b == 0x80) throw RuntimeException("Attempting to read null value as long")

    var value: Long = (b and 0x7F).toLong()
    while (b and 0x80 != 0) {
        b = this.get().toInt()
        value = value shl 7
        value = value or (b and 0x7F).toLong()
    }
    return value
}

class QrTransfer(
    private val encryptionTool: EncryptionTool,
    private val barcodeFrameworkBridge: IBarcodeFrameworkBridge,
    private val backgroud: Bitmap,
    private val hL7EncounterConverter: Lazy<HL7EncounterConverter>,
    private val appScope: CoroutineScope,
    private val waitForEncounterUseCase: WaitForEncounterUseCase,
    private val saveMaceUseCase: SaveMaceUseCase,
) {

    val TAG = "QrTransfer"

    data class PartialMessage(
        val data: ByteArray,
        val messageIndex: Long,
        val messageCount: Long,
        val messageType: IBatdokHeaderVersion,
    ) {
        fun toByteArray(): ByteArray {
            val bb =
                ByteBuffer.allocate(Int.SIZE_BYTES + messageIndex.calculateVLongSize() + messageCount.calculateVLongSize() + data.size)
            bb.putInt(MAGICNUMBER)

            bb.putVLong(messageIndex)
            bb.putVLong(messageCount)
            bb.put(data)
            return bb.array()
        }

        companion object {
            val MAGICNUMBER = ByteBuffer.wrap("BDPM".toByteArray(Charsets.UTF_8)).getInt()

            fun parseFrom(data: ByteArray, type: IBatdokHeaderVersion): PartialMessage? {

                return when (type) {
                    QrHeaderVersion.HL7_MESSAGE -> {
                        val beamMessage = QrCodeGenerator.GetBeamFromBytes(data)
                        PartialMessage(
                            data = data,
                            messageIndex = beamMessage.index.toLong(),
                            messageCount = beamMessage.count.toLong(),
                            type
                        )
                    }

                    QrHeaderVersion.BATDOK_5_0_COMPATIBLE, QrHeaderVersion.BATDOK_4_2_COMPATIBLE -> {
                        val bb = ByteBuffer.wrap(data)
                        require(
                            bb.getInt() == MAGICNUMBER,
                            { "Invalid magic number. Not a partial message byte array." })
                        val messageIndex = bb.getVLong()
                        val messageCount = bb.getVLong()
                        val messageData = ByteArray(bb.remaining())
                        bb.get(messageData)
                        PartialMessage(messageData, messageIndex, messageCount, type)
                    }

                    else -> null
                }
            }
        }

        override fun equals(other: Any?): Boolean {
            return if (other is PartialMessage) {
                messageIndex == other.messageIndex && messageCount == other.messageCount
            } else {
                false
            }
        }

        override fun hashCode(): Int {
            return java.util.Objects.hash(messageIndex, messageCount)
        }
    }

    val accumulator = mutableSetOf<PartialMessage>()
    fun clear() {
        accumulator.clear()
    }

    /**
     * returns a map that indicates which parts of the message have been received
     */
    suspend fun receiveByteArrays(
        messages: List<ByteArray>,
        context: Context,
        onFinalQRCode: () -> Unit,
    ): Map<Int, Boolean> {
        for (message in messages) {
            QrBatdokHeader.parse(message)?.let {
                try {
                    val pm: PartialMessage? =
                        PartialMessage.parseFrom(it.second, it.first.version())
                    if (pm != null) {
                        val counts = accumulator.groupBy { it.messageCount }.keys
                        if (counts.size > 1 || (counts.firstOrNull()
                                ?.let { it == pm.messageCount } == false)
                        ) {
                            Log.w(
                                TAG,
                                "Received messages with different message counts, clearing: $counts"
                            )
                            accumulator.clear()
                        }
                        accumulator.add(pm)
                        Log.d(TAG, "part ${pm.messageIndex} of ${pm.messageCount}")
                    } else {
                        Log.e(TAG, "Not a valid BATDOK QR Code")
                        return mapOf(1 to true)
                    }

                } catch (e: IllegalArgumentException) {
                    Log.i(TAG, "error parsing message", e)
                    // treat as a single message
                    receiveContactlessTransferMessages(
                        mutableListOf(
                            ContactlessTransferCommands.ContactlessTransferMessage.parseFrom(
                                QRMapperHelper.unzipBytes(
                                    encryptionTool.decryptMessage(
                                        encryptionTool.saltedHashedKey,
                                        it.second
                                    )
                                )
                            )
                        )
                    )
                    return mapOf(1 to true)
                }
            }
        }
        return if (accumulator.size == accumulator.first().messageCount.toInt()) {
            Log.d(TAG, "received all parts")
            val finaldata = accumulator.toList()
            clear()
            onFinalQRCode()
            receivePartialMessages(finaldata, context)
            (1..finaldata.first().messageCount.toInt()).associateWith { true }
        } else {
            Log.d(
                TAG,
                "waiting for more parts ${accumulator.first().messageCount - accumulator.size}"
            )
            val base = ((1..accumulator.first().messageCount.toInt()).associateWith { false })
            val received = accumulator.associate { it.messageIndex.toInt() + 1 to true }
            (base + received).also { Log.d(TAG, "received parts ${it.entries.joinToString(", ")}") }
        }
    }

    private suspend fun receivePartialMessages(messages: List<PartialMessage>, context: Context) {
        check(messages.all { it.messageCount == messages[0].messageCount })
        check(messages.size == messages[0].messageCount.toInt())
        val currentHeaderVersion = messages.first().messageType

        when (currentHeaderVersion) {
            QrHeaderVersion.HL7_MESSAGE -> {
                receiveHL7BeamMessages(messages, context)
            }

            else -> {
                try {
                    val data = unPackData(messages)
                    Log.d(TAG, "uncompressed size ${data.size}")
                    receiveContactlessTransferMessages(
                        mutableListOf(
                            ContactlessTransferCommands.ContactlessTransferMessage.parseFrom(
                                data
                            )
                        )
                    )
                } catch (exception: Exception) {
                    Log.e(TAG, "Error unpacking message", exception)
                    SafeNotify.notify(
                        context,
                        "Issue during scan. Please scan again.",
                        INotify.Type.TOAST_SHORT
                    )
                }
            }
        }
    }

    private suspend fun receiveHL7BeamMessages(
        scannedMessages: List<PartialMessage>,
        context: Context,
    ) {
        //Grab all the beam messages from the partial messages
        val beamMessageList =
            scannedMessages.map { QrCodeGenerator.GetBeamFromBytes(it.data) } as ArrayList
        //Sort by the index. If they aren't in order, the bytes won't decompress right
        beamMessageList.sortBy { it.index }

        //Get the group's supposed transfer id
        val validTransferId = beamMessageList.first().transferId
        val isCompressed = beamMessageList.first().compressed
        val isEncrypted = beamMessageList.first().encrypted
        var totalByteNumber = 0
        //Count up how many bytes we will need to decompress
        beamMessageList.forEach { totalByteNumber += it.payload.toByteArray().size }

        val fullByteArray = ByteArray(totalByteNumber)
        var lastIndexSize = 0
        try {
            beamMessageList.forEachIndexed { index, beam ->
                //Make sure the message is a part of the same transfer package
                if (beam.transferId == validTransferId) {
                    //Add together all the payload bytes
                    val beamPayload = beam.payload.toByteArray()
                    beamPayload.copyInto(fullByteArray, lastIndexSize)

                    lastIndexSize += beamPayload.size
                }
            }

            //Decompress the bytes
            val bytes = when {
                isCompressed && isEncrypted -> {
                    val unencrypted = EncryptionUtil.decryptWithPassword(fullByteArray, "opmed1776")
                    GZIPInputStream(unencrypted.inputStream()).readBytes()
                }

                isCompressed -> GZIPInputStream(fullByteArray.inputStream()).readBytes()
                isEncrypted -> EncryptionUtil.decryptWithPassword(fullByteArray, "opmed1776")
                else -> fullByteArray
            }

            when (beamMessageList.first().integration) {
                BeamOuterClass.Integration.INTEGRATION_GT -> {
                    val gtMessageBytes = Gt.GT.parseFrom(bytes)
                    //Get the HL7 message from the GT message
                    val hl7Message = gtMessageBytes.messagePayload
                    // Convert HL7 to BATDOK commands
                    val hl7Data =
                        MessageReceiver(context).processHL7Data(hl7Message.byteInputStream())
                    // Create an encounter with the commands
                    hL7EncounterConverter.value.createEncounterFromHL7(hl7Data)
                }

                BeamOuterClass.Integration.INTEGRATION_BATDOK -> {
                    val hl7Data = MessageReceiver(context).processHL7Data(bytes.inputStream())
                    // Create an encounter with the commands
                    hL7EncounterConverter.value.createEncounterFromHL7(hl7Data)
                }

                BeamOuterClass.Integration.INTEGRATION_ATMIST -> {
                    // ATMIST QR codes contain raw JSON data, not HL7
                    // The JSON data is already in the bytes array and can be processed directly
                    val jsonString = String(bytes, Charsets.UTF_8)
                    Log.d(TAG, "Received ATMIST QR code with JSON data: $jsonString")
                    publishNotification(MakeToastNotification("ATMIST QR code received", false))
                    // TODO: Process ATMIST JSON data when AtmistSummary classes are available
                }

                else -> {
                    publishNotification(MakeToastNotification("HL7 QR type is incompatible", false))
                }
            }
        } catch (exception: Exception) {
            Log.e("HL7 QR Error", exception.message ?: "", exception)
            publishNotification(MakeToastNotification("HL7 QR is malformed", false))
        }
    }

    private fun receiveContactlessTransferMessages(scannedMessages: List<ContactlessTransferCommands.ContactlessTransferMessage>) {
        check(scannedMessages.none { it.hasCompressedMultipartPacket() })

        val encounterCommands = scannedMessages.flatMap {
            if (it.hasEncounterCommand()) {
                it.bulkEncounterMessageList + it.encounterCommand
            } else {
                it.bulkEncounterMessageList
            }
        }

        // Get a list of all the patient IDs we scanned in
        val encounterIds = encounterCommands.map { it.id }.toSet()
        require(encounterIds.all { it.size() == DomainId.SIZE })

        scannedMessages.forEach {

            it.maceMessageOrNull?.let {
                appScope.launch {
                    val encounterOrNull = waitForEncounterUseCase(it.encounterId.toDomainId())!!
                    saveMaceUseCase(it, encounterOrNull.data)
                }
            }
        }

        // Add callsign iff all messages have a callsign and they all match (they should, but we should be safe)
        var senderCallsign: String? = null
        if (scannedMessages.all { it.hasSenderInfo() } && scannedMessages.all { it.senderInfo.hasCallsign() }) {
            val scannedCallsigns = scannedMessages.map { it.senderInfo.callsign }.distinct()
            if (scannedCallsigns.size == 1) {
                senderCallsign = scannedCallsigns[0]
            }
        }
        Log.d(TAG, "Encounter IDs: $encounterIds")


        encounterCommands.groupBy { it.patientId.toDomainId<PatientId>() } // turns a map of patientID into a list of messages per each ID
            .forEach { (_, messages) ->
                messages.sortedBy { it.created }.forEachIndexed { index, it ->
                    // bool based on index
                    // passed through to determine if new encounter should be made
                    val shouldNewEncounterBeMade = (index == (messages.size - 1))

                    publishNotification(
                        NfcPatientScannedNotification(
                            it,
                            senderCallsign,
                            shouldNewEncounterBeMade
                        )
                    )
                }
            }
    }

    fun packData(
        data: ByteArray,
        maxSize: Int = -1,
        qrHeaderVersion: QrHeaderVersion,
    ): List<PartialMessage> {
        val compressed = ByteArrayOutputStream()
            .use { bos -> GZIPOutputStream(bos).use { it.write(data);it.flush() }; bos.flush();bos.toByteArray() }
            .let { encryptionTool.encryptMessage(encryptionTool.saltedHashedKey, it) }
        val bytestoencode = Integer.max(
            520 - PartialMessage(
                byteArrayOf(0),
                0,
                0,
                qrHeaderVersion
            ).toByteArray().size, maxSize
        )//should result in a version 15 QR code
        val partcnt = ceil(compressed.size.toDouble() / bytestoencode.toDouble()).toInt()
        val _step = ceil(compressed.size.toDouble() / partcnt.toDouble()).toInt()
        return (compressed.indices step _step).withIndex().map {
            PartialMessage(
                compressed.copyOfRange(
                    it.value,
                    it.value + Integer.min(compressed.size - it.value, _step)
                ).also { Log.d("QRCodeGenerator", "part size ${it.size}") },
                it.index.toLong(),
                partcnt.toLong(),
                qrHeaderVersion
            )
        }
    }

    fun unPackData(messages: List<PartialMessage>): ByteArray {
        check(messages.all { it.messageCount == messages[0].messageCount })
        check(messages.size == messages[0].messageCount.toInt())

        val whole = ByteArrayOutputStream().use {
            for (pm in messages.sortedBy { it.messageIndex }) {
                it.write(pm.data)
            }
            it.flush()
            it.toByteArray()
        }
        return ByteArrayInputStream(
            encryptionTool.decryptMessage(
                encryptionTool.saltedHashedKey,
                whole
            )
        ).use { GZIPInputStream(it).use { it.readBytes() } }

    }

    fun generateQRImages(
        qrName: String,
        message: ContactlessTransferCommands.ContactlessTransferMessage,
        version: QrHeaderVersion,
        dimension: Int = 0,
    ): List<BatdokQrData> {
        val parts = packData(message.toByteArray(), qrHeaderVersion = version)
        return parts.map {
            val codeText = if (parts.size > 1) {
                "(${it.messageIndex + 1} of ${it.messageCount})"
            } else {
                qrName
            }

            barcodeFrameworkBridge.encodeQRCode(
                codeText,
                it.toByteArray(),
                IBarcodeFrameworkBridge.BarcodeFormat.QR_CODE,
                version
            ).copy(background = backgroud)

        }
    }
}