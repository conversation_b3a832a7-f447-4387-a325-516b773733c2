package com.batman.batdok.domain.usecase

import com.batman.batdok.domain.repository.CommandRepository
import com.batman.batdok.domain.repository.EncounterRepository
import com.batman.batdok.domain.usecase.encounter.GetEncounterUseCase
import com.batman.batdok.infrastructure.modes.ModePicker
import gov.afrl.batdok.commands.proto.DocumentCommands
import gov.afrl.batdok.encounter.commands.buildChangePatientIdCommand
import gov.afrl.batdok.encounter.ids.DocumentId
import gov.afrl.batdok.util.buildCommandData
import gov.afrl.batdok.util.combine
import kotlinx.coroutines.flow.firstOrNull
import mil.af.afrl.batdokdata.id.EncounterId
import mil.af.afrl.batdokdata.models.user.UserDataStore
import mil.af.afrl.batman.hl7lib.util.OutboundHL7Data
import mil.af.afrl.batman.hl7lib.util.ProviderInfo
import mil.afrl.batdok.preferences.PreferenceRepository
import mil.afrl.batdok.preferences.misc.SiteIdPreference

class BuildOutboundHL7DataUseCase(
    private val commandRepository: CommandRepository,
    private val getEncounterUseCase: GetEncounterUseCase,
    private val getPatientEncounterUseCase: GetPatientsUseCase,
    private val userDataStore: UserDataStore,
    private val preferenceRepository: PreferenceRepository,
    private val modePicker: ModePicker,
    private val encounterRepository: EncounterRepository
) {

    /**
     * Build basic OutboundHL7Data for the given encounter ID. Includes commands,
     *  provider info, the installation ID, encounter ID, mode, and any external IDs
     *  for the patient. The data returned by this use case can be amended with
     *  additional data as needed.
     */
    suspend operator fun invoke(encounterId: EncounterId) : OutboundHL7Data {
        val docCommands = commandRepository.coCommands(encounterId).flatMap { it.commandsList }
        // Add in patient ID command since it probably isn't there by default
        // The doc is usually missing the PatientID (unless it has been created by
        //  autofill) so add the PatientID here
        val providerName = userDataStore.getUsers().firstOrNull()?.name ?: ""
        val providerDodId = userDataStore.getUsers().firstOrNull()?.dodId ?: ""
        val installationId = preferenceRepository.current(SiteIdPreference)

        val activeEncounter = getEncounterUseCase(encounterId)
        val allEncountersForPatient = getPatientEncounterUseCase(encounterId).firstOrNull()
        // In future we could just change this to pass actual encounters - once HL7 library is ready
        val encountersAndCommands = mutableMapOf<EncounterId, List<DocumentCommands.CommandData>>()

        allEncountersForPatient?.forEach { encounter ->
            val docCommandForEncounterId = (commandRepository.coCommands(encounter.id).combine()?.commandsList?.toMutableList() ?: emptyList()).toMutableList()
            encounter.data.patientId.let {
                docCommandForEncounterId += buildCommandData(buildChangePatientIdCommand(it))
            }
            encountersAndCommands[encounter.id] = docCommandForEncounterId
        }

        return OutboundHL7Data().also {
            // Multiple Encounter Ids and Commands
            it.commandsByEncounter = encountersAndCommands
            it.providerInfo = ProviderInfo(providerName, providerDodId)
            it.batdokInstallationId = installationId
            // Active Encounter
            it.activeEncounterId = activeEncounter?.id
            // This isn't really being used anymore and can probably go away in future
            it.encounterId = DocumentId(encounterId.unique)
            it.mode = activeEncounter?.data?.let(modePicker::getIMode)?.name ?: ""
            it.externalIds = encounterRepository.encounterStream(encounterId).firstOrNull()?.externalIds
            // Set commandsByEncounter map for ATMIST QR generation
            it.commandsByEncounter = mapOf(encounterId to docCommands)
        }
    }

}