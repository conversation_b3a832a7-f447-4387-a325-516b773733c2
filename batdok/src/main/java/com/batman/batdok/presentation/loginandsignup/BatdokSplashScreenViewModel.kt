package com.batman.batdok.presentation.loginandsignup

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.batman.batdok.util.SnapshotExpirationManager
import kotlinx.coroutines.launch
import mil.afrl.batdok.logging.IBatdokAuditor

abstract class IBatdokSplashScreenViewModel : ViewModel() {
    abstract val canExport: Boolean
    abstract fun exportLogs()
    abstract fun expirationMessage(): String?
}

class BatdokSplashScreenViewModel(private val auditor: IBatdokAuditor,private val snapshotExpirationManager: SnapshotExpirationManager) :
    IBatdokSplashScreenViewModel() {

    override var canExport by mutableStateOf(false)
        private set

    init {
        viewModelScope.launch {
            canExport = auditor.canExport()
        }
    }

    override fun exportLogs() {
        auditor.share()
    }
    override fun expirationMessage(): String?
        = snapshotExpirationManager.getTimeLeftString()
}

class TestBatdokSplashScreenViewModel(override val canExport: Boolean = true) :
    IBatdokSplashScreenViewModel() {
    var exportLogsCalled = false

    override fun exportLogs() {
        exportLogsCalled = true
    }
    override fun expirationMessage(): String? = null
}