package com.batman.batdok.presentation.loginandsignup

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.batman.batdok.R
import gov.afrl.batman.batdok.LandscapePreview
import gov.afrl.batman.batdok.views.buttons.LabeledIconButton
import gov.batman.batdok.theme.SDCTheme
import gov.batman.batdok.theme.components.buttons.SDCButton
import org.koin.androidx.compose.koinViewModel

@Composable
fun BatdokSplashScreen(
    isSetupScreen: Boolean,
    viewModel: IBatdokSplashScreenViewModel = koinViewModel<BatdokSplashScreenViewModel>(),
    onContinue: () -> Unit
) {
    Row(modifier = Modifier.fillMaxSize()) {
        Image(
            modifier = Modifier
                .weight(2f)
                .fillMaxSize(),
            painter = painterResource(id = R.drawable.splash_screen),
            contentDescription = "Background Image",
            contentScale = ContentScale.Crop,
        )

        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .background(SDCTheme.colorScheme.defaults.fill.depth0)
                .padding(16.dp),
        ) {

            Column(
                modifier = Modifier.align(Alignment.Center),
                verticalArrangement = Arrangement.spacedBy(25.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {

                Image(
                    modifier = Modifier.fillMaxWidth(.98f),
                    painter = painterResource(id = R.drawable.batdok_logo_text),
                    contentDescription = "BATDOK",
                    contentScale = ContentScale.FillWidth,
                    colorFilter = ColorFilter.tint(LocalContentColor.current)
                )

                if (!viewModel.expirationMessage().isNullOrEmpty()) {
                    Text(
                        text = viewModel.expirationMessage()!!,
                        color = if (viewModel.expirationMessage()!!.contains("Expired"))
                            MaterialTheme.colorScheme.error
                        else
                            MaterialTheme.colorScheme.primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                SDCButton(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = onContinue,
                    enabled = viewModel.expirationMessage()?.contains("Expired", ignoreCase = true)?.not() ?: true
                ) {
                    Text(if (isSetupScreen) "Setup" else "Log in")
                }
            }

            if (viewModel.canExport) {
                LabeledIconButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomCenter),
                    iconResourceId = R.drawable.download,
                    iconContentDescription = "Export Logs",
                    labelText = "Export Logs",
                    shape = ButtonDefaults.shape
                ) {
                    viewModel.exportLogs()
                }
            }
        }
    }
}

@LandscapePreview
@Composable
fun PreviewBatdokSplashScreen() {
    SDCTheme(true) {
        Surface(modifier = Modifier.fillMaxSize()) {
            BatdokSplashScreen(
                false,
                viewModel = TestBatdokSplashScreenViewModel()
            ) { }
        }
    }
}

@Preview(widthDp = 1000, heightDp = 750)
@Composable
fun PreviewBatdokSplashScreen_Tablet() {
    SDCTheme(true) {
        Surface(modifier = Modifier.fillMaxSize()) {
            BatdokSplashScreen(
                false,
                viewModel = TestBatdokSplashScreenViewModel()
            ) { }
        }
    }
}

@LandscapePreview
@Composable
fun PreviewBatdokSplashScreen_NotSetup() {
    SDCTheme(true) {
        Surface(modifier = Modifier.fillMaxSize()) {
            BatdokSplashScreen(
                true,
                viewModel = TestBatdokSplashScreenViewModel(false)
            ) { }
        }
    }
}