package com.batman.batdok.presentation.batdok;

import static com.batman.batdok.presentation.FileAccessStrings.CACHE_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.DEFAULT_CERTS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.DEFAULT_SETTINGS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.DEP_MEDCARDS_MISSION_CARDS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.ENCRYPTION_EXTENSION;
import static com.batman.batdok.presentation.FileAccessStrings.EXPORTED_SETTINGS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.MEDCARDS_MED_DOCUMENTATION_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.MEDCARDS_MED_REFERENCE_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.MEDCARDS_MISSION_SUPPORT_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.MEDCARDS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.MEDCARDS_PJ_HANDBOOK_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.MEDCARD_PICTURES_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.MEDCARD_PLUGINS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.PATIENTS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.PATIENT_PICTURES_NAME;
import static com.batman.batdok.presentation.FileAccessStrings.PLUGINS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.REFERENCES_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.ROOT_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.SENSOR_PLUGINS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.TEAMS_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.TRAINING_AUDIO_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.UNKNOWN_NAMES_PATH;
import static com.batman.batdok.presentation.FileAccessStrings.VOICE_NOTES_PATH;

import android.app.Activity;
import android.app.Application;
import android.app.ProgressDialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.graphics.pdf.PdfDocument;
import android.net.Uri;
import android.os.Environment;
import android.util.Log;
import android.view.Surface;

import androidx.core.content.FileProvider;
import androidx.exifinterface.media.ExifInterface;

import com.batman.batdok.R;
import com.batman.batdok.infrastructure.io.IAssetsIO;
import com.batman.batdok.infrastructure.io.PluginIO;
import com.batman.batdok.presentation.FileAccessStrings;
import com.batman.batdokdialoglibrary.INotify;
import com.batman.batdokdialoglibrary.SafeNotify;
import com.tom_roush.pdfbox.pdmodel.PDDocument;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileFilter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import batdok.batman.exportlibrary.io.ExportIO;
import gov.afrl.batdok.util.Patterns;
import gov.afrl.batdok.util.TimeUtilsKt;
import mil.af.afrl.batdokdata.id.EncounterId;
import mil.af.afrl.batdokdata.models.patient.EncounterModel;
import mil.af.afrl.batdokdata.models.vital.LocationEncounterVitals;
import mil.af.afrl.batdokstorage.vital.RoomLocationVital;
import mil.afrl.batdok.preferences.PreferenceRepository;
import mil.afrl.batdok.preferences.display.DosingRecommendationPreference;
import mil.afrl.batdok.preferences.misc.CallsignPreference;

/**
 * This class holds generic IO Functions for BATDOK
 * <p>
 * Created on 4/26/2017.
 */

public class BatdokIO {

    private final String TAG = BatdokIO.class.getSimpleName();
    private final Application application;
    private final ExportIO exportIO;
    private final PreferenceRepository preferenceRepository;
    private final PluginIO pluginIO;
    private final IAssetsIO assetsIO;

    public BatdokIO(
            final Application app,
            ExportIO exportIO,
            PreferenceRepository preferenceRepository,
            PluginIO pluginIO,
            IAssetsIO assetsIO
    ) {
        application = app;
        this.exportIO = exportIO;
        this.preferenceRepository = preferenceRepository;
        this.pluginIO = pluginIO;
        this.assetsIO = assetsIO;
    }

    public void openFile(File file, String title) {
        if (file.exists()) {
            File decryptedFile = copyEncryptedToDecrypted(file.getAbsolutePath());
            Uri path = FileProvider.getUriForFile(application, Objects.requireNonNull(application).getPackageName() + ".provider", decryptedFile);

            Intent target = new Intent(Intent.ACTION_VIEW);
            target.setDataAndType(path, "application/pdf");
            target.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY);
            target.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            target.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            Intent intent = Intent.createChooser(target, title);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            try {
                application.startActivity(intent);
            } catch (ActivityNotFoundException e) {
                SafeNotify.Companion.notify(application,
                        "No Application Available to View PDF",
                        INotify.Type.TOAST_SHORT);
            }
        }
    }

    // <editor-fold desc="NORMAL IO">
    private byte[] read(String path) {
        return exportIO.getDefaultFileIO().read(path);
    }

    public void delete(String path) {
        File file = new File(path);
        if (file.isDirectory()) {
            String[] children = file.list();
            for (String child : children) {
                delete(child);
            }
        }
        if (!file.delete())
            Log.i("File deletion failure", "Deletion of file " + file.getPath() + " failed in BatdokIO.");
    }
    // </editor-fold>

    // <editor-fold desc="ENCRYPTED FILE IO">
    public byte[] readEncrypted(String path) {
        return exportIO.getEncryptedFileIO().read(path);
    }

    private File writeEncrypted(String path, byte[] data) {
        return exportIO.getEncryptedFileIO().write(path, data);
    }

    public File getCachePath() {
        return new File(application.getFilesDir().getPath() + "/" + CACHE_PATH + "/");
    }

    public File copyEncryptedToDecrypted(String path) {
        if (path.endsWith(".enc")) {
            return exportIO.getEncryptedFileIO().copyToDecrypted(path,
                    application.getFilesDir().getPath() + "/" + CACHE_PATH);
        } else {
            Log.d("Unencrypted", "File is already unencrypted");
            return new File(path);
        }
    }

    public File copyDecryptedToEncrypted(String from, String to) {
        return exportIO.getEncryptedFileIO().copyToEncrypted(from, to);
    }

    public File copyDecryptedToEncrypted(InputStream from, String to) {
        return exportIO.getEncryptedFileIO().copyToEncrypted(from, to);
    }

    public void clearCache() {
        // Move file I/O operations to background thread to avoid StrictMode violations
        new Thread(() -> {
            try {
                new File(application.getFilesDir(), CACHE_PATH).mkdirs();
                File[] files = new File(application.getFilesDir(), CACHE_PATH).listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (!file.delete())
                            Log.i("File deletion failure", "Deletion of file " + file.getPath() + " failed for clearing the cache.");
                    }
                }
            } catch (Exception e) {
                Log.e("BatdokIO", "Error clearing cache", e);
            }
        }).start();
    }
    // </editor-fold>

    // <editor-fold desc="PDF IO">
    public File writePdf(PDDocument data, String path) {
        return exportIO.getPdfIO().write(path, data);
    }

    public File writePdf(PdfDocument data, String path) {
        return exportIO.getPdfIO().write(path, data);
    }

    public File copyPdfToEncrypted(String fromPath, String password) {
        return exportIO.getPdfIO().copyToEncryptedTempFile(fromPath, application.getFilesDir().getPath() + "/" + CACHE_PATH, password);
    }
    // </editor-fold>

    // <editor-fold desc="PICTURE IO">
    public Bitmap readPicture(String path) {
        // Get the bytes of the image
        byte[] bytes = exportIO.getEncryptedFileIO().read(path);
        if (bytes == null) {
            return null;
        }

        // Get the rotation of the image of the image
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        int rotation = Surface.ROTATION_0;
        try {
            ExifInterface exif = new ExifInterface(inputStream);
            rotation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
        } catch (IOException e) {
            Log.d(TAG, "Unable to obtain EXIF data for image");
            e.printStackTrace();
        }

        // Create a matrix to rotate the bitmap if necessary
        Matrix matrix = new Matrix();
        if (rotation == Surface.ROTATION_270) matrix.postRotate(180);

        // Get the bitmap, rotate, and return it
        Bitmap originalBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);
        return Bitmap.createBitmap(originalBitmap, 0, 0, originalBitmap.getWidth(), originalBitmap.getHeight(), matrix, true);
    }

    public File writePicture(String path, Bitmap picture) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        picture.compress(Bitmap.CompressFormat.JPEG, 100, bos);
        return writeEncrypted(path, bos.toByteArray());
    }
    public File writePicturePng(String path, Bitmap picture) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        picture.compress(Bitmap.CompressFormat.PNG, 100, bos);
        return writeEncrypted(path, bos.toByteArray());
    }
    // </editor-fold>

    /**
     * Encrypts BATDOK Files
     */
    public void encryptFileSystem() {
        List<File> files = getListFiles(new File(application.getFilesDir(), PATIENTS_PATH), false);

        for (File file : files) {
            byte[] bytes = read(file.getAbsolutePath());
            writeEncrypted(file.getAbsolutePath(), bytes);
            if (!file.delete())
                Log.i("File deletion failure", "Deletion of file " + file.getPath() + " failed for encrypting file system.");
        }
    }

    /**
     * List Files for the given Directory
     *
     * @param parent       - The directory to list files from
     * @param useFilesHere - Whether to add files from the root directory or just its children
     * @return The list of files
     */
    private List<File> getListFiles(File parent, boolean useFilesHere) {
        ArrayList<File> inFiles = new ArrayList<File>();
        FileFilter fileFilter = file -> !file.getName().endsWith(ENCRYPTION_EXTENSION) && !file.getName().equals(PATIENT_PICTURES_NAME);
        parent.mkdirs();
        File[] files = parent.listFiles(fileFilter);
        for (File file : files) {
            if (file.isDirectory()) {
                inFiles.addAll(getListFiles(file, true));
            } else if (useFilesHere) {
                inFiles.add(file);
            }
        }
        return inFiles;
    }

    /**
     * Generate directories and files that are needed for BATDOK to work correctly
     *
     * @deprecated should ensure files exist at just before they are used. BATDOK-7447
     */
    @Deprecated
    public void generateBatdokFileStructure(Activity activity) {
        ProgressDialog progressDialog = new ProgressDialog(activity);
        progressDialog.setIndeterminate(true);
        progressDialog.setCancelable(false);
        progressDialog.setMessage("Building file structure");
        progressDialog.show();

        new Thread(() -> {
            //Generate Important Folders if they haven't been already
            new File(application.getFilesDir(), ROOT_PATH).mkdirs(); // make this dir first
            new File(application.getFilesDir(), PATIENTS_PATH).mkdirs();
            new File(application.getFilesDir(), MEDCARDS_PATH).mkdirs();
            new File(application.getFilesDir(), VOICE_NOTES_PATH).mkdirs();
            new File(application.getFilesDir(), MEDCARDS_PJ_HANDBOOK_PATH).mkdirs();
            new File(application.getFilesDir(), MEDCARD_PICTURES_PATH).mkdirs();
            new File(application.getFilesDir(), MEDCARDS_MED_REFERENCE_PATH).mkdirs();
            new File(application.getFilesDir(), MEDCARDS_MED_DOCUMENTATION_PATH).mkdirs();
            new File(application.getFilesDir(), TEAMS_PATH).mkdirs();
            new File(application.getFilesDir(), DEFAULT_SETTINGS_PATH).mkdirs();
            new File(application.getFilesDir(), PLUGINS_PATH).mkdirs();
            new File(application.getFilesDir(), CACHE_PATH).mkdirs();
            new File(Environment.getExternalStorageDirectory(), TRAINING_AUDIO_PATH).mkdirs();
            new File(Environment.getExternalStorageDirectory(), EXPORTED_SETTINGS_PATH).mkdirs();
            new File(Environment.getExternalStorageDirectory(), REFERENCES_PATH).mkdirs();
            new File(Environment.getExternalStorageDirectory(), SENSOR_PLUGINS_PATH).mkdirs();
            new File(Environment.getExternalStorageDirectory(), MEDCARD_PLUGINS_PATH).mkdirs();
            new File(Environment.getExternalStorageDirectory(), DEFAULT_CERTS_PATH).mkdirs();

            File oldMedCardDirectory = new File(application.getFilesDir(), DEP_MEDCARDS_MISSION_CARDS_PATH);
            if (oldMedCardDirectory.exists()) {
                if (!oldMedCardDirectory.renameTo(new File(application.getFilesDir(), MEDCARDS_MISSION_SUPPORT_PATH)))
                    Log.i("File rename failure", "Renaming file " + oldMedCardDirectory.getPath() + " failed while generating file structure.");
            } else {
                if (!new File(application.getFilesDir(), MEDCARDS_MISSION_SUPPORT_PATH).mkdirs())
                    Log.i("Mkdirs failure", "Creating directory " + MEDCARDS_MISSION_SUPPORT_PATH + " failed while " +
                            "generating file structure.");
            }

            createPatientSessionDirectory();
            writePublicKeyIfNone();

            File photosDirectory = new File(application.getFilesDir(), "Batdok/Photos");
            if (!photosDirectory.exists()) {
                if (!photosDirectory.mkdirs())
                    Log.i("Mkdirs failure", "Creating directory " + photosDirectory.getPath() + " failed while generating file structure.");
            }

            assetsIO.writeAssetToFile("Wallpaper.jpg", "Batdok/Wallpaper.jpg");
            assetsIO.writeAssetToFile("UnknownNames.txt", UNKNOWN_NAMES_PATH, true, false);
            try {
                String[] certNames = application.getAssets().list("certs");
                for (String cert : certNames) {
                    assetsIO.writeAssetToFile("certs/" + cert, DEFAULT_CERTS_PATH + "/" + cert);
                }
            } catch (IOException | NullPointerException e) {
                Log.e("BatdokIO", "Failed to copy default cert files to public storage");
            }

            progressDialog.dismiss();
        }).start();
    }

    /**
     * Create a folder for the given date to hold all patients created on that day
     */
    private void createPatientSessionDirectory() {
        // Timestamp the directory name
        String reportDate = TimeUtilsKt.format(Instant.now(), Patterns.mdy_dash, false);

        // Make the directory
        File dir = new File(application.getFilesDir(), PATIENTS_PATH + "/" + reportDate);
        if (!dir.mkdirs())
            Log.i("Mkdirs failure", "Creating directory " + dir.getPath() + " failed while creating patient session directory.");
    }

    /**
     * Get user-provided meds data, or returns the default meds data if none provided.
     * @return InputStream of meds data (the data is a stream from a CSV file)
     * @throws IOException if there is an error getting the med data.
     */
    public InputStream getMedDataStream() throws IOException {
        File medsFile = new File(application.getFilesDir(), FileAccessStrings.MED_DATA_PATH);
        if (!medsFile.exists()) {
            // Copy default data to the medsFile
            if (preferenceRepository.blockingCurrent(DosingRecommendationPreference.INSTANCE).equals("TCCC")) {
                try (InputStream tcccStream = application.getResources().openRawResource(R.raw.tccc_dosing)) {
                    copy(tcccStream, medsFile);
                }
            } else {
                try (InputStream smogStream = application.getResources().openRawResource(R.raw.smog_data)) {
                    copy(smogStream, medsFile);
                }
            }
        }
        return new FileInputStream(medsFile);
    }

    public InputStream getJDFDataStream() throws IOException {
        File jdfFile = new File(application.getFilesDir(), FileAccessStrings.JDF_DATA_PATH);
        if (!jdfFile.exists()) {
            try (InputStream jdfStream = application.getResources().openRawResource(R.raw.jdf)) {
                copy(jdfStream, jdfFile);
            }
        }
        return new FileInputStream(jdfFile);
    }

    /**
     * Copy the input stream to the destination file
     *
     * @param in  - The input to be copied
     * @param dst - Where the input is copied to.
     * @throws IOException
     */
    public void copy(InputStream in, File dst) throws IOException {
        try (OutputStream out = new FileOutputStream(dst)) {
            // Transfer bytes from in to out
            byte[] buf = new byte[1024];
            int len;
            while ((len = in.read(buf)) > 0) {
                out.write(buf, 0, len);
            }
            in.close();
        }
    }

    /**
     * If there is not a public key, then put this one in. This allows us to decrypt the password if it is forgotten
     */
    public void writePublicKeyIfNone() {
        File publicKeyFile = new File(Environment.getExternalStorageDirectory(), "Batdok/public_key.txt");
        if (!publicKeyFile.exists()) {
            if (!publicKeyFile.getParentFile().mkdirs())
                Log.i("Mkdirs failure", "Creating directory " + publicKeyFile.getParentFile().getPath() + " failed while writing public key.");
            try (PrintWriter writer = new PrintWriter(publicKeyFile)) {
                writer.write("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzQpjjnKfu1iyjeKa487w" +
                        "8SXQ7OR66dAWQRzQFnRlh/++MTHo/nZzVcr3p+Nl9OGNRSzvtdO51KR2quo9XMHT" +
                        "LAC05cdrVMEo9Kkx6vxWvOX1nuAis9EHVrVmvcpRvO1QP8dA/cqjuMeUUnHCjqKf" +
                        "2C37lBRBr2XXk15VANQoalpNQgKKtz2XlXA/lHVX9jUo7Gk8xi5tohxXacI/1Xpq" +
                        "kagrnCuzR2MtMFejjYdAnGZs1zWgEGXriwarWsl4RUDiIS6yui8NnTeTRPih8/Wb" +
                        "Tb/ews3F1xFXj+UAg/IRUwkgUGUiDXXyF23JmoAZFyGAa1qIpToPdvk9YzLYyn5b" +
                        "jwIDAQAB");
            } catch (IOException e) {
                Log.e("File not found: ", e.getMessage());
            }

        }
    }

    //TODO: duplicate function in PatientTrackingIO, consolidate these
    public String getPatientsDirectory() {
        return application.getFilesDir().getPath() + "/" + FileAccessStrings.PATIENTS_PATH;
    }

    //TODO: duplicate function in PatientTrackingIO, consolidate these

    /**
     * Create a directory for the Patient.
     * <p>
     * Uses format BATDOK/Patients/MM-dd-yyyy/Last4OfUUID
     *
     * @param encounterId  - The patient UUID
     * @param reportDate - The date the patient was created
     * @return the directory path
     */
    public String getPatientDirectory(EncounterId encounterId, Instant reportDate) {
        String hexString = encounterId.toHexString();
        String lastFour = hexString.substring(hexString.length() - 4);
        return getPatientsDirectory() + "/" + TimeUtilsKt.format(reportDate, Patterns.mdy_dash, false) + "/" + lastFour;
    }

    /**
     * Save a patient's vitals to a file
     *
     * @param patient - The Patient whose vitals you are saving
     * @param vitals  - The vitals to save to the file
     * @return The vitals file
     */
    public File generateVitalsLogForPatient(EncounterModel patient, List<LocationEncounterVitals> vitals) {
        String path = getPatientDirectory(patient.getId(), patient.getCreated()) + "/Documents/" +
                preferenceRepository.blockingCurrent(CallsignPreference.INSTANCE) + "_" + patient.getId().toHexString().substring(patient.getId().toHexString().length() - 4) + "_vitals.csv";


        StringBuilder builder = new StringBuilder();
        builder.append(RoomLocationVital.getStringHeaders());
        for (LocationEncounterVitals v : vitals) {
            builder.append(v.toString());
        }

        return writeEncrypted(path, builder.toString().getBytes());
    }
}